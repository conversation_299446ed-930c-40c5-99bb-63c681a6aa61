{"name": "cli-cursor", "version": "5.0.0", "description": "Toggle the CLI cursor", "license": "MIT", "repository": "sindresorhus/cli-cursor", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc index.d.ts"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "cursor", "ansi", "toggle", "display", "show", "hide", "term", "terminal", "console", "tty", "shell", "command-line"], "dependencies": {"restore-cursor": "^5.0.0"}, "devDependencies": {"@types/node": "^20.14.12", "ava": "^6.1.3", "typescript": "^5.5.4", "xo": "^0.59.2"}, "ava": {"workerThreads": false}}