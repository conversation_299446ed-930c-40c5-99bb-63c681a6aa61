'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';

const candidateSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phoneNumber: z.string().optional(),
  dateOfBirth: z.string().optional(),
  nationality: z.string().min(2, 'Nationality is required'),
  passportNumber: z.string().min(5, 'Passport number must be at least 5 characters'),
  studentStatus: z.boolean().default(false),
});

type CandidateFormData = z.infer<typeof candidateSchema>;

interface CreateCandidateModalProps {
  children: React.ReactNode;
}

export function CreateCandidateModal({ children }: CreateCandidateModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const form = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
    defaultValues: {
      studentStatus: false,
    },
  });

  const handleSubmit = async (data: CandidateFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          email: data.email || null,
          dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth).toISOString() : null,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create candidate');
      }

      const candidate = await response.json();
      setIsOpen(false);
      form.reset();
      router.refresh();
    } catch (error) {
      console.error('Error creating candidate:', error);
      // You could add a toast notification here
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div onClick={() => setIsOpen(true)}>
        {children}
      </div>

      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)} title="Add New Candidate">
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              {...form.register('fullName')}
              placeholder="Full Name *"
              error={form.formState.errors.fullName?.message}
            />
            
            <Input
              {...form.register('nationality')}
              placeholder="Nationality *"
              error={form.formState.errors.nationality?.message}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              {...form.register('email')}
              type="email"
              placeholder="Email Address"
              error={form.formState.errors.email?.message}
            />
            
            <Input
              {...form.register('phoneNumber')}
              placeholder="Phone Number"
              error={form.formState.errors.phoneNumber?.message}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              {...form.register('passportNumber')}
              placeholder="Passport Number *"
              error={form.formState.errors.passportNumber?.message}
            />
            
            <Input
              {...form.register('dateOfBirth')}
              type="date"
              placeholder="Date of Birth"
              error={form.formState.errors.dateOfBirth?.message}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...form.register('studentStatus')}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label className="text-sm text-gray-700">
              This candidate is a student (eligible for student discounts)
            </label>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Important Notes:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Passport number must be unique within your organization</li>
              <li>• Student status affects promotional pricing eligibility</li>
              <li>• Email is optional but recommended for notifications</li>
            </ul>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Candidate'}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  );
}
