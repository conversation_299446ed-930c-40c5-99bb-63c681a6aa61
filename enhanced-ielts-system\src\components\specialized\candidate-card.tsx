import { Candidate } from '@/types/database';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Mail, Phone, CreditCard, GraduationCap, FileText } from 'lucide-react';
import Link from 'next/link';

interface CandidateCardProps {
  candidate: Candidate;
  testCount: number;
}

export function CandidateCard({ candidate, testCount }: CandidateCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-50 rounded-lg">
            <User className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{candidate.fullName}</h3>
            <p className="text-sm text-gray-600">{candidate.nationality}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {candidate.studentStatus && (
            <Badge className="bg-green-100 text-green-800">
              Student
            </Badge>
          )}
          <Badge className="bg-blue-100 text-blue-800">
            {testCount} tests
          </Badge>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        {candidate.email && (
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-4 w-4 mr-2" />
            <span className="truncate">{candidate.email}</span>
          </div>
        )}
        
        {candidate.phoneNumber && (
          <div className="flex items-center text-sm text-gray-600">
            <Phone className="h-4 w-4 mr-2" />
            <span>{candidate.phoneNumber}</span>
          </div>
        )}
        
        <div className="flex items-center text-sm text-gray-600">
          <CreditCard className="h-4 w-4 mr-2" />
          <span>{candidate.passportNumber}</span>
        </div>
        
        <div className="flex items-center text-sm text-gray-600">
          <FileText className="h-4 w-4 mr-2" />
          <span>Registered {new Date(candidate.createdAt).toLocaleDateString()}</span>
        </div>
      </div>

      <div className="flex space-x-2">
        <Link href={`/admin/candidates/${candidate.id}`} className="flex-1">
          <Button variant="outline" size="sm" className="w-full">
            View Details
          </Button>
        </Link>
        <Link href={`/admin/candidates/${candidate.id}/tests`} className="flex-1">
          <Button variant="outline" size="sm" className="w-full">
            <FileText className="h-4 w-4 mr-1" />
            Tests
          </Button>
        </Link>
      </div>
    </div>
  );
}
