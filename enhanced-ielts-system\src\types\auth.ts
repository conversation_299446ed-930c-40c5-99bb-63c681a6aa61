import { DefaultSession, DefaultUser } from 'next-auth';
import { UserRole } from './database';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      role: UserRole;
      organizationId: string | null;
      masterAdmin: boolean;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    role: UserRole;
    organizationId: string | null;
    masterAdmin: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole;
    organizationId: string | null;
    masterAdmin: boolean;
  }
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  organizationId: string | null;
  masterAdmin: boolean;
}
