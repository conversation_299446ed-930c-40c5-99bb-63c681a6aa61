import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq, count, and, gte, desc } from 'drizzle-orm';
import { StatsCard } from '@/components/specialized/stats-card';
import { RecentResultsTable } from '@/components/specialized/recent-results-table';
import { FileText, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default async function CheckerDashboardPage() {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  // Get statistics for the checker
  const [
    totalResults,
    pendingResults,
    completedResults,
    recentResults,
  ] = await Promise.all([
    // Total results entered by this checker
    db
      .select({ count: count() })
      .from(testResults)
      .where(eq(testResults.enteredBy, session.user.id)),
    
    // Pending results (draft status)
    db
      .select({ count: count() })
      .from(testResults)
      .where(
        and(
          eq(testResults.enteredBy, session.user.id),
          eq(testResults.status, 'draft')
        )
      ),
    
    // Completed results this month
    db
      .select({ count: count() })
      .from(testResults)
      .where(
        and(
          eq(testResults.enteredBy, session.user.id),
          eq(testResults.status, 'completed'),
          gte(testResults.createdAt, new Date(new Date().getFullYear(), new Date().getMonth(), 1))
        )
      ),
    
    // Recent results entered by this checker
    db
      .select({
        id: testResults.id,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidateName: candidates.fullName,
        candidateNumber: testRegistrations.candidateNumber,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(testResults.enteredBy, session.user.id),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .orderBy(desc(testResults.createdAt))
      .limit(10),
  ]);

  const stats = [
    {
      title: 'Total Results',
      value: totalResults[0]?.count || 0,
      icon: FileText,
      description: 'Results entered by you',
      trend: '+5% from last month',
    },
    {
      title: 'Pending Review',
      value: pendingResults[0]?.count || 0,
      icon: Clock,
      description: 'Draft results',
      trend: 'Awaiting completion',
    },
    {
      title: 'This Month',
      value: completedResults[0]?.count || 0,
      icon: CheckCircle,
      description: 'Completed results',
      trend: '+12% from last month',
    },
    {
      title: 'Average Score',
      value: recentResults.length > 0 
        ? (recentResults.reduce((sum, r) => sum + parseFloat(r.overallBandScore || '0'), 0) / recentResults.length).toFixed(1)
        : '0.0',
      icon: AlertCircle,
      description: 'Recent average',
      trend: 'Last 10 results',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Checker Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's your test result entry overview.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link href="/checker/entry">
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
              <FileText className="h-8 w-8 text-blue-600 mb-2" />
              <h3 className="font-medium text-gray-900">Enter New Result</h3>
              <p className="text-sm text-gray-600">Add test results for a candidate</p>
            </div>
          </Link>
          
          <Link href="/checker/results">
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
              <Clock className="h-8 w-8 text-yellow-600 mb-2" />
              <h3 className="font-medium text-gray-900">Review Drafts</h3>
              <p className="text-sm text-gray-600">Complete pending result entries</p>
            </div>
          </Link>
          
          <Link href="/checker/results?status=completed">
            <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
              <CheckCircle className="h-8 w-8 text-green-600 mb-2" />
              <h3 className="font-medium text-gray-900">View Completed</h3>
              <p className="text-sm text-gray-600">See your completed results</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Recent Results</h2>
          <Link href="/checker/results">
            <Button variant="outline" size="sm">
              View All
            </Button>
          </Link>
        </div>
        
        <RecentResultsTable results={recentResults} />
      </div>

      {/* Performance Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {recentResults.filter(r => r.status === 'completed').length}
            </div>
            <div className="text-sm text-gray-600">Completed Today</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {recentResults.filter(r => parseFloat(r.overallBandScore || '0') >= 7).length}
            </div>
            <div className="text-sm text-gray-600">High Scores (7+)</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round((recentResults.filter(r => r.status === 'completed').length / Math.max(recentResults.length, 1)) * 100)}%
            </div>
            <div className="text-sm text-gray-600">Completion Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
}
