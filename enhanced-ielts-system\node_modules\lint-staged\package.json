{"name": "lint-staged", "version": "16.1.0", "description": "Lint files staged by git", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/lint-staged/lint-staged.git"}, "homepage": "https://github.com/lint-staged/lint-staged#readme", "bugs": {"url": "https://github.com/lint-staged/lint-staged/issues"}, "author": "<PERSON><PERSON> <<EMAIL>>", "maintainers": ["<PERSON>fty Wiranda <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (https://iiro.fi)"], "funding": {"url": "https://opencollective.com/lint-staged"}, "engines": {"node": ">=20.17"}, "type": "module", "bin": {"lint-staged": "bin/lint-staged.js"}, "exports": {".": "./lib/index.js", "./bin": "./bin/lint-staged.js", "./package.json": "./package.json"}, "files": ["bin/", "lib/", "MIGRATION.md"], "scripts": {"lint": "eslint .", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --coverage", "test:watch": "npm run test -- --watch", "typecheck": "tsc --noEmit --strict test/types/index.ts", "version": "npx changeset version", "postversion": "npm i --package-lock-only && git commit -am \"chore(changeset): release\"", "tag": "npx changeset tag"}, "dependencies": {"chalk": "^5.4.1", "commander": "^14.0.0", "debug": "^4.4.1", "lilconfig": "^3.1.3", "listr2": "^8.3.3", "micromatch": "^4.0.8", "nano-spawn": "^1.0.2", "pidtree": "^0.6.0", "string-argv": "^0.3.2", "yaml": "^2.8.0"}, "devDependencies": {"@changesets/changelog-github": "0.5.1", "@changesets/cli": "2.29.4", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/js": "9.27.0", "consolemock": "1.1.0", "cross-env": "7.0.3", "eslint": "9.27.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-jest": "28.11.0", "eslint-plugin-n": "17.18.0", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-simple-import-sort": "12.1.1", "husky": "9.1.7", "jest": "29.7.0", "jest-snapshot-serializer-ansi": "2.2.1", "mock-stdin": "1.0.0", "prettier": "3.5.3", "semver": "7.7.2", "typescript": "5.8.3"}, "keywords": ["lint", "git", "staged", "eslint", "prettier", "stylelint", "code", "quality", "check", "format", "validate"]}