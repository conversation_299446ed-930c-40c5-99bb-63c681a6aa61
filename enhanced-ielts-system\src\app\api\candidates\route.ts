import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';
import { z } from 'zod';

const createCandidateSchema = z.object({
  fullName: z.string().min(2),
  email: z.string().email().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
  dateOfBirth: z.string().optional().nullable(),
  nationality: z.string().min(2),
  passportNumber: z.string().min(5),
  studentStatus: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createCandidateSchema.parse(body);

    // Check if passport number already exists in this organization
    const existingCandidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(candidates.passportNumber, validatedData.passportNumber)
        )
      )
      .limit(1);

    if (existingCandidate.length > 0) {
      return NextResponse.json(
        { error: 'A candidate with this passport number already exists in your organization' },
        { status: 409 }
      );
    }

    // Create candidate
    const [candidate] = await db.insert(candidates).values({
      id: createId(),
      organizationId: session.user.organizationId,
      fullName: validatedData.fullName,
      email: validatedData.email || null,
      phoneNumber: validatedData.phoneNumber || null,
      dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
      nationality: validatedData.nationality,
      passportNumber: validatedData.passportNumber,
      studentStatus: validatedData.studentStatus,
      totalTests: 0,
    }).returning();

    return NextResponse.json(candidate, { status: 201 });

  } catch (error) {
    console.error('Error creating candidate:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const studentStatus = searchParams.get('studentStatus');
    const nationality = searchParams.get('nationality');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    let query = db
      .select()
      .from(candidates)
      .where(eq(candidates.organizationId, session.user.organizationId));

    // Apply filters
    const conditions = [eq(candidates.organizationId, session.user.organizationId)];
    
    if (studentStatus === 'student') {
      conditions.push(eq(candidates.studentStatus, true));
    } else if (studentStatus === 'regular') {
      conditions.push(eq(candidates.studentStatus, false));
    }
    
    if (nationality && nationality !== 'other') {
      conditions.push(eq(candidates.nationality, nationality));
    }

    // Note: For search functionality, you'd typically use a full-text search
    // or implement proper LIKE queries with proper indexing

    const results = await db
      .select()
      .from(candidates)
      .where(and(...conditions))
      .limit(limit)
      .offset((page - 1) * limit)
      .orderBy(candidates.createdAt);

    return NextResponse.json({
      candidates: results,
      pagination: {
        page,
        limit,
        total: results.length, // This should be a separate count query
      },
    });

  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
