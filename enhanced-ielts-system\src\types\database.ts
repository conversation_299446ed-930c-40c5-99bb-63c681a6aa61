import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import {
  organizations,
  users,
  candidates,
  testRegistrations,
  testResults,
  paymentTransactions,
  accessPermissions,
  promotionalRules,
  aiFeedback,
  certificateLifecycle,
} from '@/lib/db/schema';

// Select types (for reading from database)
export type Organization = InferSelectModel<typeof organizations>;
export type User = InferSelectModel<typeof users>;
export type Candidate = InferSelectModel<typeof candidates>;
export type TestRegistration = InferSelectModel<typeof testRegistrations>;
export type TestResult = InferSelectModel<typeof testResults>;
export type PaymentTransaction = InferSelectModel<typeof paymentTransactions>;
export type AccessPermission = InferSelectModel<typeof accessPermissions>;
export type PromotionalRule = InferSelectModel<typeof promotionalRules>;
export type AIFeedback = InferSelectModel<typeof aiFeedback>;
export type CertificateLifecycle = InferSelectModel<typeof certificateLifecycle>;

// Insert types (for creating new records)
export type NewOrganization = InferInsertModel<typeof organizations>;
export type NewUser = InferInsertModel<typeof users>;
export type NewCandidate = InferInsertModel<typeof candidates>;
export type NewTestRegistration = InferInsertModel<typeof testRegistrations>;
export type NewTestResult = InferInsertModel<typeof testResults>;
export type NewPaymentTransaction = InferInsertModel<typeof paymentTransactions>;
export type NewAccessPermission = InferInsertModel<typeof accessPermissions>;
export type NewPromotionalRule = InferInsertModel<typeof promotionalRules>;
export type NewAIFeedback = InferInsertModel<typeof aiFeedback>;
export type NewCertificateLifecycle = InferInsertModel<typeof certificateLifecycle>;

// Extended types with relations
export type CandidateWithTests = Candidate & {
  testRegistrations: (TestRegistration & {
    testResults: TestResult[];
  })[];
};

export type TestResultWithDetails = TestResult & {
  testRegistration: TestRegistration & {
    candidate: Candidate;
  };
  aiFeedback?: AIFeedback;
  certificate?: CertificateLifecycle;
};

export type UserWithOrganization = User & {
  organization: Organization;
};

// Enums for type safety
export type UserRole = 'admin' | 'checker';
export type OrganizationStatus = 'active' | 'suspended' | 'disabled';
export type TestStatus = 'registered' | 'completed' | 'cancelled';
export type ResultStatus = 'draft' | 'completed' | 'verified';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
export type PaymentGateway = 'click' | 'payme' | 'manual';
export type FeatureType = 'feedback' | 'certificate' | 'progress';
export type AccessType = 'paid' | 'promotional' | 'manual';
export type PromotionType = 'student_discount' | 'loyalty_reward' | 'time_based' | 'custom';
export type CertificateStatus = 'active' | 'expired' | 'deleted';
