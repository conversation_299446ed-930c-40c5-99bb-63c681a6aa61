import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates, testRegistrations } from '@/lib/db/schema';
import { eq, count, desc } from 'drizzle-orm';
import { CandidateCard } from '@/components/specialized/candidate-card';
import { CreateCandidateModal } from '@/components/specialized/create-candidate-modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Search, Users } from 'lucide-react';

export default async function CandidatesPage() {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  // Get candidates with test counts
  const candidatesWithCounts = await db
    .select({
      id: candidates.id,
      fullName: candidates.fullName,
      email: candidates.email,
      phoneNumber: candidates.phoneNumber,
      passportNumber: candidates.passportNumber,
      nationality: candidates.nationality,
      studentStatus: candidates.studentStatus,
      totalTests: candidates.totalTests,
      createdAt: candidates.createdAt,
      testCount: count(testRegistrations.id),
    })
    .from(candidates)
    .leftJoin(testRegistrations, eq(candidates.id, testRegistrations.candidateId))
    .where(eq(candidates.organizationId, session.user.organizationId))
    .groupBy(candidates.id)
    .orderBy(desc(candidates.createdAt));

  const totalCandidates = candidatesWithCounts.length;
  const studentCandidates = candidatesWithCounts.filter(c => c.studentStatus).length;
  const activeCandidates = candidatesWithCounts.filter(c => c.testCount > 0).length;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Candidates</h1>
          <p className="text-gray-600">Manage test candidates and their information</p>
        </div>
        <CreateCandidateModal>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Candidate
          </Button>
        </CreateCandidateModal>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Candidates</p>
              <p className="text-2xl font-bold text-gray-900">{totalCandidates}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Students</p>
              <p className="text-2xl font-bold text-gray-900">{studentCandidates}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-gray-900">{activeCandidates}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">New This Month</p>
              <p className="text-2xl font-bold text-gray-900">
                {candidatesWithCounts.filter(c => 
                  new Date(c.createdAt).getMonth() === new Date().getMonth()
                ).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by name, email, or passport number..."
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Candidates</option>
              <option value="student">Students Only</option>
              <option value="regular">Regular Only</option>
            </select>
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Nationalities</option>
              <option value="uzbekistan">Uzbekistan</option>
              <option value="kazakhstan">Kazakhstan</option>
              <option value="kyrgyzstan">Kyrgyzstan</option>
              <option value="tajikistan">Tajikistan</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>
      </div>

      {/* Candidates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {candidatesWithCounts.map((candidate) => (
          <CandidateCard 
            key={candidate.id} 
            candidate={{
              id: candidate.id,
              fullName: candidate.fullName,
              email: candidate.email,
              phoneNumber: candidate.phoneNumber,
              passportNumber: candidate.passportNumber,
              nationality: candidate.nationality,
              studentStatus: candidate.studentStatus,
              totalTests: candidate.totalTests,
              createdAt: candidate.createdAt,
              organizationId: session.user.organizationId!,
              dateOfBirth: null,
              photoData: null,
              updatedAt: candidate.createdAt,
            }}
            testCount={candidate.testCount}
          />
        ))}
      </div>

      {candidatesWithCounts.length === 0 && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No candidates</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by adding your first candidate.</p>
          <div className="mt-6">
            <CreateCandidateModal>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Candidate
              </Button>
            </CreateCandidateModal>
          </div>
        </div>
      )}
    </div>
  );
}
