import { auth } from '@/lib/auth/config';
import { NextResponse } from 'next/server';

export default auth((req) => {
  const { pathname } = req.nextUrl;
  const user = req.auth?.user;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/login',
    '/search',
    '/results',
    '/verify',
    '/api/public',
  ];

  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  );

  // Allow public routes
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Require authentication for protected routes
  if (!user) {
    const loginUrl = new URL('/login', req.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Role-based access control
  if (pathname.startsWith('/master')) {
    if (!user.masterAdmin) {
      return NextResponse.redirect(new URL('/admin', req.url));
    }
  }

  if (pathname.startsWith('/admin')) {
    if (user.role !== 'admin' && !user.masterAdmin) {
      return NextResponse.redirect(new URL('/checker', req.url));
    }
  }

  if (pathname.startsWith('/checker')) {
    if (user.role !== 'checker' && user.role !== 'admin' && !user.masterAdmin) {
      return NextResponse.redirect(new URL('/login', req.url));
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
