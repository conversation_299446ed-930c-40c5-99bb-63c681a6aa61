import { Organization } from '@/types/database';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, Users, Settings } from 'lucide-react';
import Link from 'next/link';

interface OrganizationCardProps {
  organization: Organization;
}

export function OrganizationCard({ organization }: OrganizationCardProps) {
  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      case 'disabled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlanColor = (plan: string | null) => {
    switch (plan) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      case 'premium':
        return 'bg-blue-100 text-blue-800';
      case 'basic':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Building2 className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{organization.name}</h3>
            <p className="text-sm text-gray-600">@{organization.slug}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Badge className={getStatusColor(organization.status)}>
            {organization.status}
          </Badge>
          <Badge className={getPlanColor(organization.billingPlan)}>
            {organization.billingPlan}
          </Badge>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex items-center text-sm text-gray-600">
          <Users className="h-4 w-4 mr-2" />
          <span>Created {new Date(organization.createdAt).toLocaleDateString()}</span>
        </div>

        {organization.features && organization.features.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {organization.features.slice(0, 3).map((feature) => (
              <span
                key={feature}
                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
              >
                {feature.replace('_', ' ')}
              </span>
            ))}
            {organization.features.length > 3 && (
              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                +{organization.features.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>

      <div className="flex space-x-2">
        <Link href={`/master/organizations/${organization.id}`} className="flex-1">
          <Button variant="outline" size="sm" className="w-full">
            <Settings className="h-4 w-4 mr-1" />
            Manage
          </Button>
        </Link>
        <Link href={`/master/organizations/${organization.id}/analytics`} className="flex-1">
          <Button variant="outline" size="sm" className="w-full">
            View Analytics
          </Button>
        </Link>
      </div>
    </div>
  );
}
