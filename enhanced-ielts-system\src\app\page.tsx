import { auth } from '@/lib/auth/config';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default async function Home() {
  const session = await auth();

  // Redirect authenticated users to their appropriate dashboard
  if (session?.user) {
    if (session.user.masterAdmin) {
      redirect('/master/dashboard');
    } else if (session.user.role === 'admin') {
      redirect('/admin/dashboard');
    } else {
      redirect('/checker/dashboard');
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col justify-center min-h-screen py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
              Enhanced IELTS
              <span className="text-blue-600"> Certification System</span>
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              A comprehensive platform for managing IELTS test centers, candidates, results, and premium features with AI-powered feedback.
            </p>

            <div className="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
              <div className="rounded-md shadow">
                <Link href="/login">
                  <Button size="lg" className="w-full sm:w-auto">
                    Sign In to Dashboard
                  </Button>
                </Link>
              </div>
              <div className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
                <Link href="/search">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Search Results
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900">Multi-Organization</h3>
                <p className="mt-2 text-gray-600">
                  Manage multiple test centers from a single master dashboard with organization-specific settings.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900">AI Feedback</h3>
                <p className="mt-2 text-gray-600">
                  Premium AI-powered feedback and study recommendations for test candidates.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900">Certificate Management</h3>
                <p className="mt-2 text-gray-600">
                  Automated certificate generation with 6-month lifecycle and secure verification.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
