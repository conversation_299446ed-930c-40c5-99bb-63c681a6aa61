import { auth } from '@/lib/auth/config';
import { redirect } from 'next/navigation';
import { Sidebar } from '@/components/layout/sidebar';
import { Header } from '@/components/layout/header';

export default async function CheckerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect('/login');
  }

  if (session.user.role !== 'checker' && session.user.role !== 'admin' && !session.user.masterAdmin) {
    redirect('/login');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header user={session.user} />
      <div className="flex">
        <Sidebar userRole={session.user.role} isMasterAdmin={session.user.masterAdmin} />
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
