import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { organizations, users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { createId } from '@paralleldrive/cuid2';
import { z } from 'zod';

const createOrganizationSchema = z.object({
  name: z.string().min(2),
  slug: z.string().min(2).regex(/^[a-z0-9-]+$/),
  adminEmail: z.string().email(),
  adminPassword: z.string().min(8),
  adminName: z.string().min(2),
  billingPlan: z.enum(['basic', 'premium', 'enterprise']),
  features: z.array(z.string()).default([]),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.masterAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createOrganizationSchema.parse(body);

    // Check if slug already exists
    const existingOrg = await db
      .select()
      .from(organizations)
      .where(eq(organizations.slug, validatedData.slug))
      .limit(1);

    if (existingOrg.length > 0) {
      return NextResponse.json(
        { error: 'Organization slug already exists' },
        { status: 409 }
      );
    }

    // Check if admin email already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, validatedData.adminEmail))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: 'Admin email already exists' },
        { status: 409 }
      );
    }

    // Create organization
    const [organization] = await db.insert(organizations).values({
      id: createId(),
      name: validatedData.name,
      slug: validatedData.slug,
      billingPlan: validatedData.billingPlan,
      features: validatedData.features,
      settings: {
        timezone: 'Asia/Tashkent',
        currency: 'UZS',
        language: 'en',
        features: validatedData.features,
      },
      status: 'active',
    }).returning();

    // Create organization admin user
    const hashedPassword = await bcrypt.hash(validatedData.adminPassword, 12);
    const [adminUser] = await db.insert(users).values({
      id: createId(),
      organizationId: organization.id,
      email: validatedData.adminEmail,
      password: hashedPassword,
      name: validatedData.adminName,
      role: 'admin',
      masterAdmin: false,
      status: 'active',
    }).returning();

    return NextResponse.json({
      organization,
      adminUser: {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating organization:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.masterAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const plan = searchParams.get('plan');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    let query = db.select().from(organizations);

    // Apply filters
    const conditions = [];
    
    if (search) {
      // Note: This would need proper text search implementation
      // For now, we'll do a simple name match
    }
    
    if (status) {
      conditions.push(eq(organizations.status, status as any));
    }
    
    if (plan) {
      conditions.push(eq(organizations.billingPlan, plan as any));
    }

    if (conditions.length > 0) {
      // Apply conditions (would need proper AND logic)
    }

    const results = await query
      .limit(limit)
      .offset((page - 1) * limit)
      .orderBy(organizations.createdAt);

    return NextResponse.json({
      organizations: results,
      pagination: {
        page,
        limit,
        total: results.length, // This should be a separate count query
      },
    });

  } catch (error) {
    console.error('Error fetching organizations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
