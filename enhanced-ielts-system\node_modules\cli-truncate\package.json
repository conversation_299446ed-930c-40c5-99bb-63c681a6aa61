{"name": "cli-truncate", "version": "4.0.0", "description": "Truncate a string to a specific width in the terminal", "license": "MIT", "repository": "sindresorhus/cli-truncate", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["truncate", "ellipsis", "text", "limit", "slice", "cli", "terminal", "term", "shell", "width", "ansi", "string"], "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^7.0.0"}, "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}}